Add mental states...like focused, relaxed, emotions positive and negative...

How i see the world, how others see me? how i see myself...

content i consume list

sytem prompts that help grow, like truth

make it more personalized to each person main concerns and problems 


--

to complex, why put all this informatin, if i want to focus on somehting i just go over it, like in wellnes, why put there, if i wwant wellnes i just focus on that 



---


rigth now there is a avalanche of ai development, we need ways to be able to stay on control, 
so for this there are 3 main problems, ai aligment, ai governance and ai human integration...

This project is my attempt to integrate the ai into humans (or humans into ai), the thing is that we need to push this integration as far as posible so we can solve the first 2 problems...


there are already a lot of systems like this, like the second brain idea, 
but here is ai native

the main problem of a system is not do not undertand you is not that is not intelligent enougth, is that you have not given them your input, i need a system that is always recolecting info about you constanly

why build a system that understand you better than you?
actually i dont know, mainly cause we have a lot of problems and we need solutions

- asking questions
- engaging in conversation with you
- you engage with them asking questions
- your personal text, videos


here the important things is also that the ai generate the information about you in a certains formats you want...or as the ai see the more convinients ways...

there is a change that is we build such a cool ai teacher, we could make it increase our iq and eq and colaborate in the biggest issues like (ai doom, global risk, flurishment of humanity)


now lets put all your energy on this mission, lets goo for the stars....

----
The system architecture:

We need a input sources this is where there is all the information about you, not procesed yet
This are basically your 5 senses

There is a output, this are the things that you do,
Now the input and output will be tigth together, in a seameles integration


Then the AI along with your help, build a Personal Information Management , this is a a library where the ai put the most revelant information about you, this is the main blueprint about you...


---
main challenges:
"Assembling the right context without proper context"
Data input recollection and processing

Map of AI

Prototype diferent futures...


Conversa...
Photos...