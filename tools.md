inputs:
    - text
    - audio
    - video
    - images


devices: 
    - phone (screen, microphone, speaker, camera)
    - computer (screen, keyboard, mouse, microphone, speaker, camera)
    - smartwatch (microphone, speaker, camera)
    - camera (microphone, speaker, camera)


we nned to recollect this information, for this we are going to use the following tools:

List of already existing tools:

🧠 In addition to Windrecorder, what other tools provide similar functions?
https://github.com/yuka-friends/Windrecorder?tab=readme-ov-file

Cross-platform Desktop:
(open source) https://github.com/louis030195/screen-pipe
(open source) https://github.com/jasonjmcghee/xrem
(open source) https://github.com/openrecall/openrecall
Windows:
(commercial) https://timesnapper.com/
(commercial) https://www.manictime.com/
(commercial) https://apse.io/
(commercial) https://www.screen-record.com/screen_anytime.htm
Linux:
(open source) https://github.com/apirrone/Memento
MacOS:
(open source) https://github.com/jasonjmcghee/rem
(commercial) https://screenmemory.app
(commercial) https://www.rewind.ai/
Android:
(free, in-app purchases) https://play.google.com/store/apps/details?id=io.github.mthli.snapseek


Link to the tools i personally use:
IDE(vscode, cursor,etc) where i can write and organize my files


Testing: Tools to record my computer and phone


Link to the tools i am creating:


I need to organize my thougths and ideas , for this i need a second brain, urgently.

In the nigth i think i just should talk and write to the ai, so i need a easy way to do this in my phone..