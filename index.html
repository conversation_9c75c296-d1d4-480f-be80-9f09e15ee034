<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="HumOS: Your Personal AI Integration Framework. Enhance collaboration with AI through structured personal profiles for truly personalized assistance.">
    <meta name="keywords" content="AI integration, HumOS, personal AI assistant, AI collaboration, machine learning, personal development, productivity enhancement, AI profile system, Human-AI interaction">
    <meta name="author" content="HumOS">
    <meta property="og:title" content="HumOS: Your Personal AI Integration Framework">
    <meta property="og:description" content="Optimize your AI interactions with HumOS - A structured framework for personalized AI collaboration and growth">
    <meta property="og:type" content="website">
    <meta property="og:image" content="favicon.svg">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="HumOS: Your Personal AI Integration Framework">
    <meta name="twitter:description" content="Create your personal operating manual for AI with HumOS - A comprehensive framework for enhanced AI collaboration.">
    <link rel="icon" type="image/svg+xml" href="favicon.svg">
    <title>HumOS: Your Personal AI Integration Framework</title>
    <!-- Add Marked.js -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <style>
        :root {
            --primary-color: #60a5fa;
            --text-color: #e5e7eb;
            --bg-color: #111827;
            --section-bg: #1f2937;
            --header-bg: #0f172a;
            --nav-bg: #1e3a8a;
            --hover-bg: rgba(255, 255, 255, 0.1);
            --gradient-start: #1e3a8a;
            --gradient-end: #3b82f6;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
            scroll-behavior: smooth;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
            line-height: 1.8;
            color: var(--text-color);
            background-color: var(--bg-color);
            transition: all 0.3s ease;
        }

        header {
            background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
            color: white;
            padding: 4rem 2rem;
            text-align: center;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        header p {
            max-width: 600px;
            margin: 1rem auto;
            opacity: 0.9;
        }

        nav {
            background-color: rgba(30, 58, 138, 0.95);
            padding: 1rem;
            position: sticky;
            top: 0;
            z-index: 100;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        nav ul {
            list-style: none;
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 1.5rem;
            max-width: 1200px;
            margin: 0 auto;
            position: relative;
        }

        nav ul .github-link {
            position: absolute;
            right: 0;
        }

        nav ul .github-link a {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
            padding: 0.5rem 1rem;
            border-radius: 2rem;
            transition: all 0.3s ease;
        }

        nav ul .github-link a:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            background: linear-gradient(135deg, var(--gradient-end), var(--gradient-start));
        }

        nav ul .github-link svg {
            margin-right: 0.25rem;
        }

        @media (max-width: 768px) {
            nav ul .github-link {
                position: static;
                margin-top: 1rem;
            }
        }

        nav a {
            color: white;
            text-decoration: none;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
            font-weight: 500;
            position: relative;
        }

        nav a:hover {
            background-color: var(--hover-bg);
            transform: translateY(-2px);
        }

        nav a.active {
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--primary-color);
        }

        main {
            max-width: 1200px;
            margin: 0 auto;
            padding: 3rem 2rem;
            min-height: 400px;
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: var(--primary-color);
        }

        .loading::after {
            content: "Loading...";
            animation: dots 1.5s infinite;
        }

        @keyframes dots {
            0%, 20% { content: "Loading."; }
            40% { content: "Loading.."; }
            60%, 100% { content: "Loading..."; }
        }

        .error {
            color: #ef4444;
            padding: 2rem;
            text-align: center;
            background: rgba(239, 68, 68, 0.1);
            border-radius: 0.5rem;
            margin: 2rem 0;
        }

        .section {
            background-color: var(--section-bg);
            border-radius: 1rem;
            padding: 2.5rem;
            margin: 2rem auto;
            max-width: 1200px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .content-header {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 2rem;
        }

        /* Markdown content styling */
        .section h1 {
            font-size: 2.5rem;
            margin: 2rem 0 1rem;
            color: var(--primary-color);
        }

        .section h2 {
            font-size: 2rem;
            margin: 2rem 0 1rem;
            color: var(--primary-color);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding-bottom: 0.5rem;
        }

        .section h3 {
            font-size: 1.75rem;
            margin: 1.5rem 0 1rem;
            color: #93c5fd;
        }

        .section blockquote {
            border-left: 4px solid var(--primary-color);
            margin: 1.5rem 0;
            padding-left: 1rem;
            color: #9ca3af;
        }

        .section pre {
            background-color: var(--header-bg);
            padding: 1rem;
            border-radius: 0.5rem;
            overflow-x: auto;
            margin: 1.5rem 0;
        }

        .section code {
            background-color: var(--header-bg);
            padding: 0.2rem 0.4rem;
            border-radius: 0.25rem;
            font-family: monospace;
        }

        .copy-btn {
            background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .copy-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .copy-btn.success {
            background: linear-gradient(135deg, #059669, #34d399);
        }

        h1 { /* This is the main header H1, not section H1 */
            font-size: 3rem;
            margin-bottom: 1rem;
            color: white;
            font-weight: 700;
            letter-spacing: -0.025em;
        }

        h2 {
            font-size: 2.25rem;
            margin-bottom: 1.5rem;
            color: var(--primary-color);
            font-weight: 700;
            letter-spacing: -0.025em;
        }

        h3 {
            font-size: 1.75rem;
            margin-bottom: 1.5rem;
            color: #93c5fd;
            font-weight: 600;
        }

        h4 {
            font-size: 1.25rem;
            margin-bottom: 1rem;
            color: var(--primary-color);
            font-weight: 600;
        }

        p {
            margin-bottom: 1.5rem;
            max-width: 70ch;
            line-height: 1.8;
        }

        ul {
            margin-left: 2rem;
            margin-bottom: 1.5rem;
            line-height: 1.8;
        }

        ul ul {
            margin-bottom: 0.5rem;
        }

        li {
            margin-bottom: 0.75rem;
        }

        strong {
            color: #93c5fd;
            font-weight: 600;
        }

        a {
            color: var(--primary-color);
            text-decoration: none;
            transition: all 0.3s ease;
            border-bottom: 1px solid transparent;
        }

        a:hover {
            color: #93c5fd;
            border-bottom-color: currentColor;
        }

        @media (max-width: 768px) {
            h1 {
                font-size: 2rem;
            }

            h2 {
                font-size: 1.75rem;
            }

            nav ul {
                flex-direction: column;
                align-items: center;
            }

            .section {
                padding: 1.5rem;
            }

            main {
                padding: 1.5rem;
            }

            .section-header {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <header>
        <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg" style="margin-bottom: 1rem;">
            <path d="M32 8C22 8 14 16 14 26C14 32 18 37 24 39V44L32 48L40 44V39C46 37 50 32 50 26C50 16 42 8 32 8Z" stroke="white" stroke-width="2" fill="none"/>
            <circle cx="32" cy="26" r="8" stroke="white" stroke-width="2" fill="none"/>
            <path d="M28 26C28 26 30 28 32 28C34 28 36 26 36 26" stroke="white" stroke-width="2" fill="none"/>
            <path d="M24 39C24 39 28 42 32 42C36 42 40 39 40 39" stroke="white" stroke-width="2" fill="none"/>
            <circle cx="26" cy="23" r="1" fill="white"/>
            <circle cx="38" cy="23" r="1" fill="white"/>
            <!-- Gears -->
            <circle cx="18" cy="18" r="4" stroke="white" stroke-width="2" fill="none"/>
            <circle cx="46" cy="18" r="4" stroke="white" stroke-width="2" fill="none"/>
            <path d="M18 14L18 10M18 26L18 22M14 18L10 18M26 18L22 18M15 15L12 12M21 21L24 24M21 15L24 12M15 21L12 24" stroke="white" stroke-width="1"/>
            <path d="M46 14L46 10M46 26L46 22M42 18L38 18M54 18L50 18M43 15L40 12M49 21L52 24M49 15L52 12M43 21L40 24" stroke="white" stroke-width="1"/>
        </svg>
        <h1>HumOS: Your Personal AI Integration Framework</h1>
        <p>Your Personal Interface for Enhanced AI Collaboration</p>
    </header>

    <nav>
        <ul>
            <li><a href="#purpose">Purpose</a></li>
            <li><a href="#template">Template</a></li>
            <li><a href="#questionnaire">Questionnaire</a></li>
            <li><a href="#use-cases">Use Cases</a></li>
            <li><a href="#philosophy">Philosophy</a></li>

            <li class="github-link"><a href="https://github.com/lout33/HumOS" target="_blank" rel="noopener noreferrer">
                <svg height="20" width="20" viewBox="0 0 16 16" fill="currentColor">
                    <path d="M8 0C3.58 0 0 3.58 0 8c0 3.54 2.29 6.53 5.47 7.59.4.07.55-.17.55-.38 0-.19-.01-.82-.01-1.49-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 1.23.82.72 1.21 1.87.87 2.33.66.07-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.02.08-2.12 0 0 .67-.21 2.2.82.64-.18 1.32-.27 2-.27.68 0 1.36.09 2 .27 1.53-1.04 2.2-.82 2.2-.82.44 1.1.16 1.92.08 **********.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 **********.54.73.54 1.48 0 1.07-.01 1.93-.01 2.2 0 .***********.38A8.013 8.013 0 0016 8c0-4.42-3.58-8-8-8z"></path>
                </svg>
                Open Source
            </a></li>
        </ul>
    </nav>

    <main id="content" class="section">
        <div class="content-header">
            <button id="copyButton" class="copy-btn">Copy Content</button>
        </div>
    </main>

    <script>
        // Function to load and render markdown content
        async function loadMarkdown(file) {
            const content = document.getElementById('content');
            content.innerHTML = '<div class="loading"></div>';
            
            try {
                const response = await fetch(file);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const text = await response.text();
                const mainContent = marked.parse(text);
                
                content.innerHTML = `
                    <div class="content-header">
                        <button id="copyButton" class="copy-btn">Copy Content</button>
                    </div>
                    ${mainContent}
                `;
                
                // Add copy functionality
                const copyButton = document.getElementById('copyButton');
                copyButton.addEventListener('click', async () => {
                    try {
                        const contentDiv = document.getElementById('content');
                        const textToCopy = contentDiv.innerText.replace('Copy Content', '').trim();
                        await navigator.clipboard.writeText(textToCopy);
                        copyButton.textContent = 'Copied!';
                        copyButton.classList.add('success');
                        setTimeout(() => {
                            copyButton.textContent = 'Copy Content';
                            copyButton.classList.remove('success');
                        }, 2000);
                    } catch (err) {
                        console.error('Failed to copy text:', err);
                        copyButton.textContent = 'Failed to copy';
                        setTimeout(() => {
                            copyButton.textContent = 'Copy Content';
                        }, 2000);
                    }
                });
                
                // Update active navigation link
                document.querySelectorAll('nav a').forEach(link => {
                    link.classList.remove('active');
                    if (link.getAttribute('data-md') === file) {
                        link.classList.add('active');
                    }
                });
            } catch (error) {
                console.error('Error loading markdown:', error);
                content.innerHTML = '<div class="error">Error loading content. Please try again.</div>';
            }
        }

        // Update navigation links to load markdown files
        document.addEventListener('DOMContentLoaded', () => {
            const links = {
                'readme.MD': '#purpose',
                'template02.md': '#template',
                'questionnaire.md': '#questionnaire',
                'use_cases.md': '#use-cases',
                'philosophy.md': '#philosophy'
            };

            for (const [file, selector] of Object.entries(links)) {
                const link = document.querySelector(`a[href="${selector}"]`);
                if (link) {
                    link.setAttribute('data-md', file);
                    link.addEventListener('click', (e) => {
                        e.preventDefault();
                        loadMarkdown(file);
                    });
                }
            }

            // Load initial content
            loadMarkdown('readme.MD');
        });
    </script>
</body>
</html>